# frozen_string_literal: true

require "sentry/rails/log_subscriber"

module Sentry
  module Rails
    module LogSubscribers
      # LogSubscriber for ActiveJob events that captures background job execution
      # and logs them using Sentry's structured logging system.
      #
      # This subscriber captures various ActiveJob events including job execution,
      # enqueueing, retries, and failures with relevant job information.
      #
      # @example Usage
      #   # Enable structured logging for ActiveJob
      #   Sentry.init do |config|
      #     config.enable_logs = true
      #     config.rails.structured_logging = true
      #     config.rails.structured_logging.attach_to = [:active_job]
      #   end
      class ActiveJobSubscriber < Sentry::Rails::LogSubscriber
        # Handle perform.active_job events
        #
        # @param event [ActiveSupport::Notifications::Event] The job performance event
        def perform(event)
          return if excluded_event?(event)

          job = event.payload[:job]
          duration = duration_ms(event)

          attributes = {
            job_class: job.class.name,
            job_id: job.job_id,
            queue_name: job.queue_name,
            duration_ms: duration,
            executions: job.executions,
            priority: job.priority
          }

          attributes[:adapter] = job.class.queue_adapter.class.name if job.class.respond_to?(:queue_adapter)

          if job.scheduled_at
            attributes[:scheduled_at] = job.scheduled_at.iso8601
            attributes[:delay_ms] = ((Time.current - job.scheduled_at) * 1000).round(2)
          end

          if Sentry.configuration.send_default_pii && job.arguments.present?
            filtered_args = filter_sensitive_arguments(job.arguments)
            attributes[:arguments] = filtered_args unless filtered_args.empty?
          end

          message = "Job performed: #{job.class.name}"
          level = level_for_duration(duration, 30000)

          log_structured_event(
            message: message,
            level: level,
            attributes: attributes
          )
        end

        # Handle enqueue.active_job events
        #
        # @param event [ActiveSupport::Notifications::Event] The job enqueue event
        def enqueue(event)
          return if excluded_event?(event)

          job = event.payload[:job]

          attributes = {
            job_class: job.class.name,
            job_id: job.job_id,
            queue_name: job.queue_name,
            priority: job.priority
          }

          attributes[:adapter] = job.class.queue_adapter.class.name if job.class.respond_to?(:queue_adapter)

          if job.scheduled_at
            attributes[:scheduled_at] = job.scheduled_at.iso8601
            attributes[:delay_seconds] = (job.scheduled_at - Time.current).round(2)
          end

          message = "Job enqueued: #{job.class.name}"

          log_structured_event(
            message: message,
            level: :info,
            attributes: attributes
          )
        end

        def retry_stopped(event)
          return if excluded_event?(event)

          job = event.payload[:job]
          error = event.payload[:error]

          attributes = {
            job_class: job.class.name,
            job_id: job.job_id,
            queue_name: job.queue_name,
            executions: job.executions,
            error_class: error.class.name,
            error_message: error.message
          }

          message = "Job retry stopped: #{job.class.name}"

          log_structured_event(
            message: message,
            level: :error,
            attributes: attributes
          )
        end

        def discard(event)
          return if excluded_event?(event)

          job = event.payload[:job]
          error = event.payload[:error]

          attributes = {
            job_class: job.class.name,
            job_id: job.job_id,
            queue_name: job.queue_name,
            executions: job.executions
          }

          attributes[:error_class] = error.class.name if error
          attributes[:error_message] = error.message if error

          message = "Job discarded: #{job.class.name}"

          log_structured_event(
            message: message,
            level: :warn,
            attributes: attributes
          )
        end

        private

        def filter_sensitive_arguments(arguments)
          return [] unless arguments.is_a?(Array)

          arguments.map do |arg|
            case arg
            when Hash
              filter_sensitive_hash(arg)
            when String
              arg.length > 100 ? "[FILTERED: #{arg.length} chars]" : arg
            else
              arg
            end
          end
        end

        def filter_sensitive_hash(hash)
          sensitive_keys = %w[
            password token secret api_key
            email personal_data user_data
            credit_card ssn social_security_number
          ]

          hash.reject do |key, _value|
            key_str = key.to_s.downcase
            sensitive_keys.any? { |sensitive| key_str.include?(sensitive) }
          end
        end
      end
    end
  end
end
