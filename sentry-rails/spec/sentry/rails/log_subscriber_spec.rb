# frozen_string_literal: true

require "spec_helper"

RSpec.describe Sentry::Rails::LogSubscriber do
  describe "base functionality" do
    let(:subscriber) { described_class.new }

    describe "#excluded_event?" do
      it "excludes events starting with !" do
        event = double("event", name: "!connection.active_record", payload: {})
        expect(subscriber.send(:excluded_event?, event)).to be true
      end

      it "allows normal events" do
        event = double("event", name: "sql.active_record", payload: { name: "User Load" })
        expect(subscriber.send(:excluded_event?, event)).to be false
      end
    end

    describe "#duration_ms" do
      it "calculates duration in milliseconds" do
        event = double("event", duration: 123.456789)
        expect(subscriber.send(:duration_ms, event)).to eq(123.46)
      end
    end

    describe "#level_for_duration" do
      it "returns info for fast operations" do
        expect(subscriber.send(:level_for_duration, 500)).to eq(:info)
      end

      it "returns warn for slow operations" do
        expect(subscriber.send(:level_for_duration, 1500)).to eq(:warn)
      end

      it "uses custom threshold" do
        expect(subscriber.send(:level_for_duration, 500, 300)).to eq(:warn)
      end
    end

    describe "#log_structured_event" do
      before do
        make_basic_app do |config|
          config.enable_logs = true
        end
      end

      it "logs events with correct message and attributes" do
        subscriber.send(:log_structured_event,
          message: "Test message",
          level: :info,
          attributes: { test_attr: "value" }
        )

        # Flush the log event buffer to ensure events are processed
        Sentry.get_current_client.log_event_buffer.flush

        # Verify the logged event
        expect(sentry_logs).not_to be_empty

        log_event = sentry_logs.last
        expect(log_event[:level]).to eq("info")
        expect(log_event[:body]).to eq("Test message")

        # Debug: let's see what attributes are actually present
        puts "First test attributes: #{log_event[:attributes].inspect}"

        expect(log_event[:attributes][:test_attr]).to eq({ value: "value", type: "string" })
      end

      it "logs events with different levels" do
        # Clear any existing logs first
        initial_log_count = sentry_logs.count

        subscriber.send(:log_structured_event,
          message: "Warning message",
          level: :warn,
          attributes: { severity: "high" }
        )

        # Flush the log event buffer to ensure events are processed
        Sentry.get_current_client.log_event_buffer.flush

        # Verify the logged event
        expect(sentry_logs).not_to be_empty
        expect(sentry_logs.count).to eq(initial_log_count + 1)

        # Find the specific log event we just created
        log_event = sentry_logs.find { |log| log[:body] == "Warning message" }
        expect(log_event).not_to be_nil
        expect(log_event[:level]).to eq("warn")
        expect(log_event[:body]).to eq("Warning message")

        # Debug: let's see what attributes are actually present
        puts "Warning message attributes: #{log_event[:attributes].inspect}"

        expect(log_event[:attributes][:severity]).to eq({ value: "high", type: "string" })
      end

      it "logs events without custom attributes" do
        subscriber.send(:log_structured_event,
          message: "Simple message",
          level: :debug,
          attributes: {}
        )

        # Flush the log event buffer to ensure events are processed
        Sentry.get_current_client.log_event_buffer.flush

        # Verify the logged event
        expect(sentry_logs).not_to be_empty

        log_event = sentry_logs.last
        expect(log_event[:level]).to eq("debug")
        expect(log_event[:body]).to eq("Simple message")

        # Should only have default Sentry attributes, no custom ones
        custom_attributes = log_event[:attributes].reject { |key, _| key.to_s.start_with?("sentry.") || key == :parameters }
        expect(custom_attributes).to be_empty
      end

      it "handles errors gracefully" do
        # Simulate an error in the logging system by temporarily disabling logs
        original_enable_logs = Sentry.configuration.enable_logs
        Sentry.configuration.enable_logs = false

        expect {
          subscriber.send(:log_structured_event,
            message: "Test message",
            level: :info,
            attributes: {}
          )
        }.not_to raise_error

        # Restore the original setting
        Sentry.configuration.enable_logs = original_enable_logs
      end

      context "when logging is disabled" do
        before do
          make_basic_app do |config|
            config.enable_logs = false
          end
        end

        it "does not log events when logging is disabled" do
          initial_log_count = sentry_logs.count

          subscriber.send(:log_structured_event,
            message: "Test message",
            level: :info,
            attributes: { test_attr: "value" }
          )

          # Try to flush if the buffer exists
          if Sentry.get_current_client&.log_event_buffer
            Sentry.get_current_client.log_event_buffer.flush
          end

          # Verify no new events were logged
          expect(sentry_logs.count).to eq(initial_log_count)
        end
      end
    end
  end

  describe "attach_to behavior" do
    it "sets logger to nil to prevent standard Rails logging" do
      subscriber_class = Class.new(described_class)
      subscriber_class.attach_to :test_component

      expect(subscriber_class.logger).to be_nil
    end
  end
end
